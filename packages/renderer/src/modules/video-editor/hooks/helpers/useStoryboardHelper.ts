import { useCallback } from 'react'
import { Overlay, OverlayType, StoryboardOverlay, VideoOverlay } from '@clipnest/remotion-shared/types'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { byStartFrame, findStoryboardIndex, byStoryboard } from '@/modules/video-editor/utils/overlay-helper'
import { DEFAULT_OVERLAY } from '@/modules/video-editor/constants'
import { generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { cloneDeep } from 'lodash'
import { TrackType } from '@/modules/video-editor/types'

export const useStoryboardHelper = () => {
  const { updateTracks } = useEditorContext()

  /**
   * 在 `base` 分镜的右方插入 `amount` 个新的分镜
   */
  const pushStoryboardAtRightOf = useCallback(
    (base: StoryboardOverlay, amount = 1) => {
      updateTracks(prevTracks => {
        const clonedTracks = cloneDeep(prevTracks)
        const totalPushedDistance = amount * DEFAULT_OVERLAY.durationInFrames
        const newId = generateNewOverlayId(prevTracks)

        for (const track of clonedTracks) {
          // 分镜所在的轨道, 需要后移后方的分镜
          if (track.type === TrackType.STORYBOARD) {
            const newStoryboards = Array
              .from({ length: amount })
              .map((_, index) => ({
                ...DEFAULT_OVERLAY,
                id: newId + index,
                type: OverlayType.STORYBOARD,
                from: base.from + base.durationInFrames + index * DEFAULT_OVERLAY.durationInFrames,
              } as Overlay))

            track.overlays.forEach(overlay => {
              if (overlay.from > base.from) {
                overlay.from += totalPushedDistance
              }
            })
            track.overlays.push(...newStoryboards)
            track.overlays.sort(byStartFrame())
          }

          // 非全局轨道, 需要后移后方的 Overlay
          if (!track.isGlobalTrack) {
            const baseStoryboardIndex = findStoryboardIndex(prevTracks, base)
            if (baseStoryboardIndex !== -1) {
              for (const overlay of track.overlays) {
                if (overlay.storyboardIndex !== undefined && overlay.storyboardIndex > baseStoryboardIndex) {
                  overlay.from += totalPushedDistance
                  overlay.storyboardIndex += amount
                }
              }
            }
          }
        }

        return clonedTracks
      })
    },
    []
  )

  /**
   * 关闭分镜下所有视频的声音
   */
  const setVolumeForVideosInStoryboard = useCallback(
    (storyboard: StoryboardOverlay, volume: number) => {
      updateTracks(prevTracks => {
        const clonedTracks = cloneDeep(prevTracks)
        const storyboardIndex = findStoryboardIndex(prevTracks, storyboard)

        for (const track of clonedTracks) {
          if (track.isGlobalTrack || track.type === TrackType.STORYBOARD) continue

          for (const overlay of track.overlays) {
            if (overlay.type === OverlayType.VIDEO && overlay.storyboardIndex === storyboardIndex) {
              overlay.styles.volume = volume
            }
          }
        }

        return clonedTracks
      })
    },
    []
  )

  /**
   * 调整分镜下所有视频的时长
   */
  const adjustDurationOfVideosInStoryboard = useCallback(
    (storyboard: StoryboardOverlay) => {
      updateTracks(prevTracks => {
        const clonedTracks = cloneDeep(prevTracks)
        const storyboardIndex = findStoryboardIndex(prevTracks, storyboard)

        if (storyboardIndex === -1) {
          console.warn('无法找到指定的分镜')
          return prevTracks
        }

        // 遍历所有视频轨道
        for (const track of clonedTracks) {
          // 跳过全局轨道和分镜轨道
          if (track.isGlobalTrack || track.type === TrackType.STORYBOARD) continue

          // 筛选出属于该分镜的视频 Overlay
          const storyboardVideos = track.overlays
            .filter(byStoryboard({ ...storyboard, index: storyboardIndex }))
            .filter(o => o.type === OverlayType.VIDEO)
            .sort(byStartFrame()) as VideoOverlay[]

          // 如果该轨道在此分镜下没有视频，跳过
          if (storyboardVideos.length === 0) continue

          // 找到最后一个视频 Overlay
          const lastVideo = storyboardVideos[storyboardVideos.length - 1]

          // 检查最后一个视频是否有原始时长信息
          if (!lastVideo.originalDurationInFrames || lastVideo.originalDurationInFrames <= 0) {
            console.warn(`视频 ${lastVideo.id} 缺少原始时长信息，跳过调整`)
            continue
          }

          // 计算其他视频的总时长
          const otherVideosTotalDuration = storyboardVideos
            .filter(video => video.id !== lastVideo.id)
            .reduce((total, video) => total + video.durationInFrames, 0)

          // 计算最后一个视频需要的新时长
          const targetNewDuration = storyboard.durationInFrames - otherVideosTotalDuration

          if (targetNewDuration <= 0) {
            console.warn(`分镜 ${storyboard.id} 在轨道中剩余时长不足，无法调整最后一个视频的时长`)
            continue
          }

          // 计算新的播放速度
          const newSpeed = lastVideo.originalDurationInFrames / targetNewDuration
          const finalDuration = Math.round(lastVideo.originalDurationInFrames / newSpeed)

          // 找到最后一个视频在轨道中的索引并更新
          const videoIndex = track.overlays.findIndex(o => o.id === lastVideo.id)
          if (videoIndex !== -1) {
            track.overlays[videoIndex] = {
              ...lastVideo,
              speed: newSpeed,
              durationInFrames: finalDuration
            }
          }
        }

        return clonedTracks
      })
    },
    [updateTracks]
  )

  /**
   * 清空分镜下所有 Overlay
   */
  const clearOverlaysInStoryboard = useCallback(
    (storyboard: StoryboardOverlay) => {
      return updateTracks(prevTracks => {
        const storyboardIndex = findStoryboardIndex(prevTracks, storyboard)

        return prevTracks.map(track => {
          if (track.type === TrackType.STORYBOARD || track.isGlobalTrack) return track

          return {
            ...track,
            overlays: track.overlays.filter(o => o.storyboardIndex !== storyboardIndex)
          }
        })
      })
    },
    []
  )

  /**
   * 保存分镜素材至素材库
   */
  const saveMaterialToLibrary = useCallback(
    (_storyboard: StoryboardOverlay) => {

    },
    []
  )

  return {
    clearOverlaysInStoryboard,
    pushStoryboardAtRightOf,
    setVolumeForVideosInStoryboard,
    adjustDurationOfVideosInStoryboard,
    saveMaterialToLibrary,
  }
}
