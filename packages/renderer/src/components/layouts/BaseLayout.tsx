import clsx from 'clsx'
import React, { useEffect, useState } from 'react'
import { NavLink, Outlet } from 'react-router-dom'
import type { MenuItem } from './HomeLayout'
import { Check, ChevronRight, Moon, Plus, Sun } from 'lucide-react'
import { UserMenu } from '@/components/auth/UserMenu'
import { Avatar, AvatarFallback } from '../ui/avatar'
import { useQueryCurrentTeam, useQueryTeamList } from '@/hooks/queries/useQueryTeam'
import { Button } from '../ui/button'
import { randomGradient } from '../fake-image'
import { cn } from '../lib/utils'
import { Dialog, DialogContent, DialogTitle } from '../ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Input } from '../ui/input'
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '../ui/dropdown-menu'
import { TeamManager } from '@/libs/storage'
import { TeamAPI } from '@/libs/request/api/team'
import { toast } from 'react-toastify'
import { useQueryClient } from '@tanstack/react-query'
import { Skeleton } from '../ui/skeleton'

interface BaseLayoutProps {
  menuItems: MenuItem[]
  sidebarClassName?: string
}

const DarkModeToggleButton = () => {
  const [darkMode, setDarkMode] = useState(true) // 默认使用暗色主题

  // 切换主题
  const toggleDarkMode = () => {
    const newDarkMode = !darkMode
    setDarkMode(newDarkMode)

    // 保存设置到本地存储
    localStorage.setItem('theme', newDarkMode ? 'dark' : 'light')

    // 应用主题
    if (newDarkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  // 初始化暗色主题
  useEffect(() => {
    // 从本地存储中获取主题设置，如果没有则默认使用暗色
    const savedTheme = localStorage.getItem('theme')
    const isDark = savedTheme ? savedTheme === 'dark' : true

    setDarkMode(isDark)

    // 应用主题
    if (isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [])

  // {/* 日夜模式切换按钮 */}
  return (
    <button
      className="p-2 rounded hover:bg-neutral-200 dark:hover:bg-neutral-700"
      onClick={toggleDarkMode}
      title={darkMode ? '切换到亮色模式' : '切换到暗色模式'}
    >
      {darkMode ? <Sun /> : <Moon />}
    </button>
  )
}

const TeamSwitcher = () => {
  const queryClient = useQueryClient()
  const [menuOpen, setMenuOpen] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const { data: team } = useQueryCurrentTeam()
  const { data: teams } = useQueryTeamList()

  const selectTeam = async (teamId: number) => {
    setMenuOpen(false)
    if (team?.id === teamId) return // 如果已选中则不切换
    try {
      const valid = await TeamAPI.check({ teamId })
      if (!valid) throw new Error()
      await TeamManager.switch(teamId)
      await queryClient.resetQueries()
    } catch (error) {
      toast.error('切换团队失败，团队不存在或已被删除')
    }
  }

  const CreateContent = () => {
    const errorMapping = (msg?: string) => {
      if (!msg) return '创建团队失败'
      if (msg.endsWith('已存在')) return '团队名称已存在，请更换名称后重试'
      return msg
    }

    const [pending, setPending] = useState(false)
    const [name, setName] = useState('')
    const [error, setError] = useState('')

    const handleCreateTeam = async () => {
      try {
        setPending(true)
        if (!name.trim()) throw new Error('团队名称不能为空')
        setError('') // 清除之前的错误信息
        const teamId = await TeamAPI.create({ name })
        TeamManager.switch(teamId)
        await queryClient.resetQueries()
        setDialogOpen(false) // 关闭对话框
      } catch (error: any) {
        setError(errorMapping(error?.message))
      } finally {
        setPending(false)
      }
    }

    return (
      <TabsContent value="create" className="flex-1 flex flex-col gap-4">
        <DialogTitle>创建新团队</DialogTitle>

        <div className="flex flex-col gap-2">
          <div className="relative">
            <Input
              placeholder="输入团队名称"
              className="pr-14"
              value={name}
              onChange={e => setName(e.target.value.slice(0, 20))}
            />
            <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
              {name.length}/20
            </span>
          </div>
          {error && <p className="text-sm text-destructive">{error}</p>}
        </div>
        <Button className="mt-auto" disabled={pending} onClick={handleCreateTeam}>
          {pending ? <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-blue-500" /> : '创建团队'}
        </Button>
      </TabsContent>
    )
  }

  const JoinContent = () => {
    const [pending, setPending] = useState(false)
    const [inviteCode, setInviteCode] = useState('')
    const [error, setError] = useState('')

    const handleJoinTeam = async () => {
      try {
        setPending(true)
        if (!inviteCode.trim()) throw new Error('邀请码不能为空')
        if (inviteCode.length !== 32) throw new Error('邀请码长度必须为32位')
        setError('') // 清除之前的错误信息
        const teamId = await TeamAPI.join({ inviteCode })
        await TeamManager.switch(teamId)
        await queryClient.resetQueries()
        setDialogOpen(false) // 关闭对话框
      } catch (error: any) {
        setError(error?.message || '加入团队失败')
      } finally {
        setPending(false)
      }
    }

    return (
      <TabsContent value="join" className="flex-1 flex flex-col gap-4">
        <DialogTitle>加入现有团队</DialogTitle>
        <div className="flex flex-col gap-2">
          <Input placeholder="输入团队邀请码" value={inviteCode} onChange={e => setInviteCode(e.target.value)} />
          {error && <p className="text-sm text-destructive">{error}</p>}
        </div>
        <Button className="mt-auto" disabled={pending} onClick={handleJoinTeam}>
          {pending ? <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-blue-500" /> : '加入团队'}
        </Button>
      </TabsContent>
    )
  }

  return (
    <>
      <DropdownMenu open={menuOpen} onOpenChange={setMenuOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="lg" className="flex gap-2 p-2 h-10 group">
            {team ? (
              <>
                <Avatar className="size-8">
                  <AvatarFallback style={{ backgroundImage: randomGradient(team.id) }}>
                    {team.name.slice(0, 1)}
                  </AvatarFallback>
                </Avatar>
                <h3 className="text-lg font-bold">{team.name}</h3>
                <ChevronRight className={cn('size-4 transition-all group-data-[state=open]:rotate-90')} />
              </>
            ) : (
              <>
                <Skeleton className="size-8 rounded-full" />
                <Skeleton className="w-20 h-6 rounded" />
                <ChevronRight className="size-4 ml-auto" />
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-64 p-2 *:h-12">
          {teams ? (
            <>
              {teams.map(t => (
                <Button
                  key={t.id}
                  variant="ghost"
                  className="flex gap-2 w-full justify-start px-2"
                  onClick={() => selectTeam(t.id)}
                >
                  <Avatar className="size-8">
                    <AvatarFallback style={{ backgroundImage: randomGradient(t.id) }}>
                      {t.name.slice(0, 1)}
                    </AvatarFallback>
                  </Avatar>
                  <span>{t.name}</span>
                  {t.id === team?.id && <Check className="size-4 ml-auto" />}
                </Button>
              ))}
              <Button
                variant="ghost"
                className="flex gap-2 w-full justify-start"
                onClick={() => {
                  setMenuOpen(false)
                  setDialogOpen(true)
                }}
              >
                <Plus className="size-4" />
                加入/创建团队
              </Button>
            </>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-blue-500" />
            </div>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="w-100 h-60">
          <Tabs defaultValue="create" className="flex flex-col">
            <TabsList className="flex gap-2 bg-background" defaultValue="create">
              <TabsTrigger value="join">加入团队</TabsTrigger>
              <TabsTrigger value="create">创建团队</TabsTrigger>
            </TabsList>
            <CreateContent />
            <JoinContent />
          </Tabs>
        </DialogContent>
      </Dialog>
    </>
  )
}

const HeaderToolbar = () => {
  return (
    <div className=" h-14 px-6 flex items-center justify-between border-b border-neutral-700">
      <TeamSwitcher />
      <div className="flex items-center space-x-8">
        <div className="flex gap-3 items-center">
          <DarkModeToggleButton />
        </div>

        {/* 用户菜单 - 包含个人设置和退出登录功能 */}
        <UserMenu />
      </div>
    </div>
  )
}

export const BaseLayout: React.FC<BaseLayoutProps> = ({ menuItems, sidebarClassName }) => {
  return (
    <div
      className={`
        flex flex-col w-full h-full bg-cover bg-center bg-no-repeat
        [background-image:url(/images/bg-dashboard.jpg)]
        dark:[background-image:url(/images/bg-dark-dashboard.jpg)]
      `}
    >
      <HeaderToolbar />

      <div className="flex-1 flex overflow-hidden py-6 px-3 gap-4">
        {/* 侧边栏 */}
        <aside className={clsx(' transition-all duration-300 w-50  ', sidebarClassName)}>
          {/* 菜单区域 */}
          <nav className="ml-6 mt-6">
            <div className="flex flex-col gap-2">
              {menuItems.map(item => (
                <div key={item.key} className="h-10">
                  <NavLink
                    to={item.path}
                    className={({ isActive }) => {
                      return clsx(
                        'flex items-center px-3 py-2 h-full rounded-xl transition-colors text-sm',
                        isActive ? 'bg-gradient-brand  text-black font-bold' : 'hover:bg-neutral-700',
                      )
                    }}
                  >
                    <span className="mr-3">{item.icon}</span>
                    <span>{item.title}</span>
                  </NavLink>
                </div>
              ))}
            </div>
          </nav>
        </aside>

        {/* 主内容区域 */}
        <div className="flex flex-col flex-1 overflow-hidden ">
          {/* 内容区域 - 滚动容器 */}
          <main className="flex-1 overflow-hidden ">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  )
}
